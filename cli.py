import os
import sys
import logging

# 添加项目根目录到Python路径，以便正确导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

from config import Config
from quiz_processor.document_to_quiz_processor import DocumentToQuizProcessor
from utils.helpers import (
    setup_logging, validate_input_directory, create_argument_parser,
    print_banner, print_progress_bar, validate_api_config,
    estimate_processing_time, summarize_results, scan_directory_structure
)
from error_handler.error_manager import ErrorManager

logger = logging.getLogger(__name__)

def main():
    print_banner()

    # 确保存在默认配置文件
    Config.create_default_config_if_missing()

    parser = create_argument_parser()
    args = parser.parse_args()

    # 设置日志级别
    log_level = "DEBUG" if args.verbose else "INFO"
    setup_logging(log_level)

    logger.info("正在启动命令行应用程序...")

    # 加载配置
    config = Config.load_config()
    config = Config.update_from_args(config, args) # 从命令行参数更新配置

    # 验证输入目录
    if not validate_input_directory(args.input_dir):
        logger.error(f"输入目录无效或不存在: {args.input_dir}")
        sys.exit(1)

    # 如果指定了清理错误文件
    if args.cleanup_errors:
        error_manager = ErrorManager(config.ERROR_DIR)
        error_manager.cleanup_old_errors(args.cleanup_errors)
        logger.info(f"已清理 {args.cleanup_errors} 天前的错误文件。")
        sys.exit(0)

    # 仅测试API连接
    if args.test_connection:
        logger.info("正在测试API连接...")
        if validate_api_config(config):
            logger.info("✅ API连接成功！")
            sys.exit(0)
        else:
            logger.error("❌ API连接失败。请检查您的API密钥和API基础URL。")
            sys.exit(1)

    # 验证API配置
    if not validate_api_config(config):
        logger.error("API配置无效。请确保已设置API密钥或API基础URL。")
        sys.exit(1)

    try:
        processor = DocumentToQuizProcessor(config)

        # 扫描目录结构并处理文档
        # 注意：此处省略了递归处理文档的复杂逻辑，
        # 实际应用中需要根据 scan_directory_structure 和 processor.process_document 实现
        # 这里仅作示例性的调用
        all_files_to_process = scan_directory_structure(args.input_dir, recursive=not args.no_recursive)
        
        if not all_files_to_process:
            logger.info("没有找到需要处理的文档。")
            return

        logger.info(f"找到 {len(all_files_to_process)} 个文件需要处理。")
        
        # 估算处理时间
        estimated_time = estimate_processing_time(len(all_files_to_process))
        logger.info(f"预计处理时间: 约 {estimated_time:.2f} 分钟")

        processed_count = 0
        successful_count = 0
        failed_count = 0

        for i, doc_path in enumerate(all_files_to_process):
            logger.info(f"正在处理文件 [{i+1}/{len(all_files_to_process)}]: {doc_path}")
            try:
                # 假设process_document返回成功或失败状态
                success = processor.process_document(doc_path)
                if success:
                    successful_count += 1
                else:
                    failed_count += 1
            except Exception as e:
                logger.error(f"处理文件 {doc_path} 时发生错误: {e}")
                failed_count += 1
            finally:
                processed_count += 1
                print_progress_bar(processed_count, len(all_files_to_process))
        
        summarize_results(successful_count, failed_count)

    except Exception as e:
        logger.exception("命令行应用程序运行中发生未预期的错误:")
        sys.exit(1)

if __name__ == "__main__":
    main() 