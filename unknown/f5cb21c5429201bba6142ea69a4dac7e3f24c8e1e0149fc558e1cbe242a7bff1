<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:numbering xmlns:mv="urn:schemas-microsoft-com:mac:vml" xmlns:mo="http://schemas.microsoft.com/office/mac/office/2008/main" xmlns:ve="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml" xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing">
	<w:abstractNum w:abstractNumId="0">
		<w:nsid w:val="FFFFFF1D"/>
		<w:multiLevelType w:val="multilevel"/>
		<w:tmpl w:val="D0409C7C"/>
		<w:lvl w:ilvl="0">
			<w:start w:val="1"/>
			<w:numFmt w:val="bullet"/>
			<w:lvlText w:val=""/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="0"/>
				</w:tabs>
				<w:ind w:left="0" w:firstLine="0"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default"/>
			</w:rPr>
		</w:lvl>
		<w:lvl w:ilvl="1">
			<w:start w:val="1"/>
			<w:numFmt w:val="bullet"/>
			<w:lvlText w:val=""/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="720"/>
				</w:tabs>
				<w:ind w:left="1080" w:hanging="360"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default"/>
			</w:rPr>
		</w:lvl>
		<w:lvl w:ilvl="2">
			<w:start w:val="1"/>
			<w:numFmt w:val="bullet"/>
			<w:lvlText w:val="o"/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="1440"/>
				</w:tabs>
				<w:ind w:left="1800" w:hanging="360"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Courier New" w:hAnsi="Courier New" w:hint="default"/>
			</w:rPr>
		</w:lvl>
		<w:lvl w:ilvl="3">
			<w:start w:val="1"/>
			<w:numFmt w:val="bullet"/>
			<w:lvlText w:val=""/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="2160"/>
				</w:tabs>
				<w:ind w:left="2520" w:hanging="360"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Wingdings" w:hAnsi="Wingdings" w:hint="default"/>
			</w:rPr>
		</w:lvl>
		<w:lvl w:ilvl="4">
			<w:start w:val="1"/>
			<w:numFmt w:val="bullet"/>
			<w:lvlText w:val=""/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="2880"/>
				</w:tabs>
				<w:ind w:left="3240" w:hanging="360"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Wingdings" w:hAnsi="Wingdings" w:hint="default"/>
			</w:rPr>
		</w:lvl>
		<w:lvl w:ilvl="5">
			<w:start w:val="1"/>
			<w:numFmt w:val="bullet"/>
			<w:lvlText w:val=""/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="3600"/>
				</w:tabs>
				<w:ind w:left="3960" w:hanging="360"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default"/>
			</w:rPr>
		</w:lvl>
		<w:lvl w:ilvl="6">
			<w:start w:val="1"/>
			<w:numFmt w:val="bullet"/>
			<w:lvlText w:val="o"/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="4320"/>
				</w:tabs>
				<w:ind w:left="4680" w:hanging="360"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Courier New" w:hAnsi="Courier New" w:hint="default"/>
			</w:rPr>
		</w:lvl>
		<w:lvl w:ilvl="7">
			<w:start w:val="1"/>
			<w:numFmt w:val="bullet"/>
			<w:lvlText w:val=""/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="5040"/>
				</w:tabs>
				<w:ind w:left="5400" w:hanging="360"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Wingdings" w:hAnsi="Wingdings" w:hint="default"/>
			</w:rPr>
		</w:lvl>
		<w:lvl w:ilvl="8">
			<w:start w:val="1"/>
			<w:numFmt w:val="bullet"/>
			<w:lvlText w:val=""/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="5760"/>
				</w:tabs>
				<w:ind w:left="6120" w:hanging="360"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Wingdings" w:hAnsi="Wingdings" w:hint="default"/>
			</w:rPr>
		</w:lvl>
	</w:abstractNum>
	<w:abstractNum w:abstractNumId="1">
		<w:nsid w:val="FFFFFF7C"/>
		<w:multiLevelType w:val="singleLevel"/>
		<w:tmpl w:val="9B522538"/>
		<w:lvl w:ilvl="0">
			<w:start w:val="1"/>
			<w:numFmt w:val="decimal"/>
			<w:lvlText w:val="%1."/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="1492"/>
				</w:tabs>
				<w:ind w:left="1492" w:hanging="360"/>
			</w:pPr>
		</w:lvl>
	</w:abstractNum>
	<w:abstractNum w:abstractNumId="2">
		<w:nsid w:val="FFFFFF7D"/>
		<w:multiLevelType w:val="singleLevel"/>
		<w:tmpl w:val="1BB4178C"/>
		<w:lvl w:ilvl="0">
			<w:start w:val="1"/>
			<w:numFmt w:val="decimal"/>
			<w:lvlText w:val="%1."/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="1209"/>
				</w:tabs>
				<w:ind w:left="1209" w:hanging="360"/>
			</w:pPr>
		</w:lvl>
	</w:abstractNum>
	<w:abstractNum w:abstractNumId="3">
		<w:nsid w:val="FFFFFF7E"/>
		<w:multiLevelType w:val="singleLevel"/>
		<w:tmpl w:val="4720F336"/>
		<w:lvl w:ilvl="0">
			<w:start w:val="1"/>
			<w:numFmt w:val="decimal"/>
			<w:lvlText w:val="%1."/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="926"/>
				</w:tabs>
				<w:ind w:left="926" w:hanging="360"/>
			</w:pPr>
		</w:lvl>
	</w:abstractNum>
	<w:abstractNum w:abstractNumId="4">
		<w:nsid w:val="FFFFFF7F"/>
		<w:multiLevelType w:val="singleLevel"/>
		<w:tmpl w:val="7DCEC040"/>
		<w:lvl w:ilvl="0">
			<w:start w:val="1"/>
			<w:numFmt w:val="decimal"/>
			<w:lvlText w:val="%1."/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="643"/>
				</w:tabs>
				<w:ind w:left="643" w:hanging="360"/>
			</w:pPr>
		</w:lvl>
	</w:abstractNum>
	<w:abstractNum w:abstractNumId="5">
		<w:nsid w:val="FFFFFF80"/>
		<w:multiLevelType w:val="singleLevel"/>
		<w:tmpl w:val="A01021FA"/>
		<w:lvl w:ilvl="0">
			<w:start w:val="1"/>
			<w:numFmt w:val="bullet"/>
			<w:lvlText w:val=""/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="1492"/>
				</w:tabs>
				<w:ind w:left="1492" w:hanging="360"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default"/>
			</w:rPr>
		</w:lvl>
	</w:abstractNum>
	<w:abstractNum w:abstractNumId="6">
		<w:nsid w:val="FFFFFF81"/>
		<w:multiLevelType w:val="singleLevel"/>
		<w:tmpl w:val="977AAC7C"/>
		<w:lvl w:ilvl="0">
			<w:start w:val="1"/>
			<w:numFmt w:val="bullet"/>
			<w:lvlText w:val=""/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="1209"/>
				</w:tabs>
				<w:ind w:left="1209" w:hanging="360"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default"/>
			</w:rPr>
		</w:lvl>
	</w:abstractNum>
	<w:abstractNum w:abstractNumId="7">
		<w:nsid w:val="FFFFFF82"/>
		<w:multiLevelType w:val="singleLevel"/>
		<w:tmpl w:val="0D26C678"/>
		<w:lvl w:ilvl="0">
			<w:start w:val="1"/>
			<w:numFmt w:val="bullet"/>
			<w:lvlText w:val=""/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="926"/>
				</w:tabs>
				<w:ind w:left="926" w:hanging="360"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default"/>
			</w:rPr>
		</w:lvl>
	</w:abstractNum>
	<w:abstractNum w:abstractNumId="8">
		<w:nsid w:val="FFFFFF83"/>
		<w:multiLevelType w:val="singleLevel"/>
		<w:tmpl w:val="EE200B76"/>
		<w:lvl w:ilvl="0">
			<w:start w:val="1"/>
			<w:numFmt w:val="bullet"/>
			<w:lvlText w:val=""/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="643"/>
				</w:tabs>
				<w:ind w:left="643" w:hanging="360"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default"/>
			</w:rPr>
		</w:lvl>
	</w:abstractNum>
	<w:abstractNum w:abstractNumId="9">
		<w:nsid w:val="FFFFFF88"/>
		<w:multiLevelType w:val="singleLevel"/>
		<w:tmpl w:val="E7381406"/>
		<w:lvl w:ilvl="0">
			<w:start w:val="1"/>
			<w:numFmt w:val="decimal"/>
			<w:pStyle w:val="ListNumber"/>
			<w:lvlText w:val="%1."/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="360"/>
				</w:tabs>
				<w:ind w:left="360" w:hanging="360"/>
			</w:pPr>
		</w:lvl>
	</w:abstractNum>
	<w:abstractNum w:abstractNumId="10">
		<w:nsid w:val="FFFFFF89"/>
		<w:multiLevelType w:val="singleLevel"/>
		<w:tmpl w:val="ECFC1548"/>
		<w:lvl w:ilvl="0">
			<w:start w:val="1"/>
			<w:numFmt w:val="bullet"/>
			<w:pStyle w:val="ListBullet"/>
			<w:lvlText w:val=""/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="360"/>
				</w:tabs>
				<w:ind w:left="360" w:hanging="360"/>
			</w:pPr>
			<w:rPr>
				<w:rFonts w:ascii="Symbol" w:hAnsi="Symbol" w:hint="default"/>
			</w:rPr>
		</w:lvl>
	</w:abstractNum>
	<w:abstractNum w:abstractNumId="11">
		<w:nsid w:val="30585973"/>
		<w:multiLevelType w:val="multilevel"/>
		<w:tmpl w:val="A47E15CE"/>
		<w:lvl w:ilvl="0">
			<w:start w:val="1"/>
			<w:numFmt w:val="decimal"/>
			<w:lvlText w:val="%1."/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="720"/>
				</w:tabs>
				<w:ind w:left="720" w:hanging="720"/>
			</w:pPr>
		</w:lvl>
		<w:lvl w:ilvl="1">
			<w:start w:val="1"/>
			<w:numFmt w:val="decimal"/>
			<w:lvlText w:val="%2."/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="1440"/>
				</w:tabs>
				<w:ind w:left="1440" w:hanging="720"/>
			</w:pPr>
		</w:lvl>
		<w:lvl w:ilvl="2">
			<w:start w:val="1"/>
			<w:numFmt w:val="decimal"/>
			<w:lvlText w:val="%3."/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="2160"/>
				</w:tabs>
				<w:ind w:left="2160" w:hanging="720"/>
			</w:pPr>
		</w:lvl>
		<w:lvl w:ilvl="3">
			<w:start w:val="1"/>
			<w:numFmt w:val="decimal"/>
			<w:lvlText w:val="%4."/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="2880"/>
				</w:tabs>
				<w:ind w:left="2880" w:hanging="720"/>
			</w:pPr>
		</w:lvl>
		<w:lvl w:ilvl="4">
			<w:start w:val="1"/>
			<w:numFmt w:val="decimal"/>
			<w:lvlText w:val="%5."/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="3600"/>
				</w:tabs>
				<w:ind w:left="3600" w:hanging="720"/>
			</w:pPr>
		</w:lvl>
		<w:lvl w:ilvl="5">
			<w:start w:val="1"/>
			<w:numFmt w:val="decimal"/>
			<w:lvlText w:val="%6."/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="4320"/>
				</w:tabs>
				<w:ind w:left="4320" w:hanging="720"/>
			</w:pPr>
		</w:lvl>
		<w:lvl w:ilvl="6">
			<w:start w:val="1"/>
			<w:numFmt w:val="decimal"/>
			<w:lvlText w:val="%7."/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="5040"/>
				</w:tabs>
				<w:ind w:left="5040" w:hanging="720"/>
			</w:pPr>
		</w:lvl>
		<w:lvl w:ilvl="7">
			<w:start w:val="1"/>
			<w:numFmt w:val="decimal"/>
			<w:lvlText w:val="%8."/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="5760"/>
				</w:tabs>
				<w:ind w:left="5760" w:hanging="720"/>
			</w:pPr>
		</w:lvl>
		<w:lvl w:ilvl="8">
			<w:start w:val="1"/>
			<w:numFmt w:val="decimal"/>
			<w:lvlText w:val="%9."/>
			<w:lvlJc w:val="left"/>
			<w:pPr>
				<w:tabs>
					<w:tab w:val="num" w:pos="6480"/>
				</w:tabs>
				<w:ind w:left="6480" w:hanging="720"/>
			</w:pPr>
		</w:lvl>
	</w:abstractNum>
	<w:num w:numId="1">
		<w:abstractNumId w:val="11"/>
	</w:num>
	<w:num w:numId="2">
		<w:abstractNumId w:val="11"/>
		<w:lvlOverride w:ilvl="0">
			<w:startOverride w:val="1"/>
		</w:lvlOverride>
		<w:lvlOverride w:ilvl="1">
			<w:startOverride w:val="1"/>
		</w:lvlOverride>
		<w:lvlOverride w:ilvl="2">
			<w:startOverride w:val="1"/>
		</w:lvlOverride>
		<w:lvlOverride w:ilvl="3">
			<w:startOverride w:val="1"/>
		</w:lvlOverride>
		<w:lvlOverride w:ilvl="4">
			<w:startOverride w:val="1"/>
		</w:lvlOverride>
		<w:lvlOverride w:ilvl="5">
			<w:startOverride w:val="1"/>
		</w:lvlOverride>
		<w:lvlOverride w:ilvl="6">
			<w:startOverride w:val="1"/>
		</w:lvlOverride>
		<w:lvlOverride w:ilvl="7">
			<w:startOverride w:val="1"/>
		</w:lvlOverride>
		<w:lvlOverride w:ilvl="8">
			<w:startOverride w:val="1"/>
		</w:lvlOverride>
	</w:num>
	<w:num w:numId="3">
		<w:abstractNumId w:val="4"/>
	</w:num>
	<w:num w:numId="4">
		<w:abstractNumId w:val="3"/>
	</w:num>
	<w:num w:numId="5">
		<w:abstractNumId w:val="2"/>
	</w:num>
	<w:num w:numId="6">
		<w:abstractNumId w:val="1"/>
	</w:num>
	<w:num w:numId="7">
		<w:abstractNumId w:val="0"/>
	</w:num>
	<w:num w:numId="8">
		<w:abstractNumId w:val="10"/>
	</w:num>
	<w:num w:numId="9">
		<w:abstractNumId w:val="8"/>
	</w:num>
	<w:num w:numId="10">
		<w:abstractNumId w:val="7"/>
	</w:num>
	<w:num w:numId="11">
		<w:abstractNumId w:val="6"/>
	</w:num>
	<w:num w:numId="12">
		<w:abstractNumId w:val="5"/>
	</w:num>
	<w:num w:numId="13">
		<w:abstractNumId w:val="9"/>
	</w:num>
</w:numbering>
