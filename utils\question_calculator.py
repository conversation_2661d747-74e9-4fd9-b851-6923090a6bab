"""
题目数量计算器
根据文本字数和基础配置计算各类题目的数量
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class QuestionCalculator:
    """题目数量计算器"""
    
    def __init__(self, config):
        """
        初始化题目数量计算器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.base_char_threshold = getattr(config, 'BASE_CHAR_THRESHOLD', 2000)
        
        # 基础题目数量（使用配置中的题目数量作为基础）
        self.base_single_choice = getattr(config, 'SINGLE_CHOICE_COUNT', 2)
        self.base_multiple_choice = getattr(config, 'MULTIPLE_CHOICE_COUNT', 1)
        self.base_fill_blank = getattr(config, 'FILL_BLANK_COUNT', 1)
        self.base_short_answer = getattr(config, 'SHORT_ANSWER_COUNT', 1)
        self.base_true_false = getattr(config, 'TRUE_FALSE_COUNT', 1)
        self.base_sorting = getattr(config, 'SORTING_COUNT', 0)
        
        logger.info(f"题目数量计算器初始化: 基础字数阈值={self.base_char_threshold}")
        logger.info(f"基础题目数量: 单选={self.base_single_choice}, 多选={self.base_multiple_choice}, "
                   f"填空={self.base_fill_blank}, 简答={self.base_short_answer}, "
                   f"判断={self.base_true_false}, 排序={self.base_sorting}")
    
    def calculate_question_counts(self, char_count: int) -> Dict[str, int]:
        """
        根据字符数计算各类题目的数量
        
        Args:
            char_count: 文本字符数
            
        Returns:
            各类题目数量的字典
        """
        # 1. 字符数为0或负数时，直接返回空题目数量
        if char_count <= 0:
            logger.warning(f"字符数 {char_count} 为0或负数，返回空题目数量。")
            return {
                'single_choice_count': 0,
                'multiple_choice_count': 0,
                'fill_blank_count': 0,
                'short_answer_count': 0,
                'true_false_count': 0,
                'sorting_count': 0
            }

        # 2. 基础字数阈值为0时，无法计算倍数，返回空题目数量
        if self.base_char_threshold == 0:
            logger.warning("基础字数阈值为0，无法计算倍数，返回空题目数量。")
            return {
                'single_choice_count': 0,
                'multiple_choice_count': 0,
                'fill_blank_count': 0,
                'short_answer_count': 0,
                'true_false_count': 0,
                'sorting_count': 0
            }

        question_counts = {}
        # 3. 字数小于等于基础字数阈值时，直接使用基础题目数量
        if char_count <= self.base_char_threshold:
            logger.debug(f"字符数 {char_count} 小于等于基础阈值 {self.base_char_threshold}，直接使用基础题目数量。")
            question_counts = {
                'single_choice_count': self.base_single_choice,
                'multiple_choice_count': self.base_multiple_choice,
                'fill_blank_count': self.base_fill_blank,
                'short_answer_count': self.base_short_answer,
                'true_false_count': self.base_true_false,
                'sorting_count': self.base_sorting
            }
            ratio = 1.0 # 仅用于日志输出
        # 4. 字数大于基础字数阈值时，计算倍率并取整
        else:
            ratio = char_count / self.base_char_threshold
            logger.debug(f"字符数 {char_count} 大于基础阈值 {self.base_char_threshold}，倍率为 {ratio:.2f}。")
            question_counts = {
                'single_choice_count': int(self.base_single_choice * ratio),
                'multiple_choice_count': int(self.base_multiple_choice * ratio),
                'fill_blank_count': int(self.base_fill_blank * ratio),
                'short_answer_count': int(self.base_short_answer * ratio),
                'true_false_count': int(self.base_true_false * ratio),
                'sorting_count': int(self.base_sorting * ratio)
            }

        # 5. 确保对于非零基础数量的题型，在计算后如果结果为0，至少生成1道题目。
        #    此处的 char_count 必然 > 0 (因为已经在函数开头处理了 char_count <= 0 的情况)
        for q_type in question_counts:
            base_count_attr = f"base_{q_type.replace('_count', '')}"
            if getattr(self, base_count_attr, 0) > 0 and question_counts[q_type] == 0:
                question_counts[q_type] = 1

        total_questions = sum(question_counts.values())
        logger.info(f"字符数 {char_count} -> 倍率 {ratio:.2f} -> 总题目数 {total_questions}")
        logger.debug(f"题目分布: {question_counts}")
        
        return question_counts
    
    def get_question_counts_for_chunk(self, chunk: Dict[str, Any]) -> Dict[str, int]:
        """
        为特定文本块计算题目数量
        
        Args:
            chunk: 文本块信息
            
        Returns:
            题目数量字典
        """
        char_count = chunk.get('char_count', 0)
        if char_count == 0:
            # 如果没有字符数信息，尝试计算
            content = chunk.get('content', '')
            char_count = len(content.strip())
            
        filename = chunk.get('filename', '未知文件')
        chunk_index = chunk.get('chunk_index', 0)
        
        logger.debug(f"为文本块计算题目数量: {filename} (块 {chunk_index}), 字符数: {char_count}")
        
        return self.calculate_question_counts(char_count)
    
    def update_config_with_calculated_counts(self, config, question_counts: Dict[str, int]):
        """
        用计算出的题目数量更新配置对象
        
        Args:
            config: 配置对象
            question_counts: 计算出的题目数量
        """
        config.SINGLE_CHOICE_COUNT = question_counts.get('single_choice_count', self.base_single_choice)
        config.MULTIPLE_CHOICE_COUNT = question_counts.get('multiple_choice_count', self.base_multiple_choice)
        config.FILL_BLANK_COUNT = question_counts.get('fill_blank_count', self.base_fill_blank)
        config.SHORT_ANSWER_COUNT = question_counts.get('short_answer_count', self.base_short_answer)
        config.TRUE_FALSE_COUNT = question_counts.get('true_false_count', self.base_true_false)
        config.SORTING_COUNT = question_counts.get('sorting_count', self.base_sorting)
        
        logger.debug(f"配置已更新: 单选={config.SINGLE_CHOICE_COUNT}, 多选={config.MULTIPLE_CHOICE_COUNT}, "
                    f"填空={config.FILL_BLANK_COUNT}, 简答={config.SHORT_ANSWER_COUNT}, "
                    f"判断={config.TRUE_FALSE_COUNT}, 排序={config.SORTING_COUNT}")
    
    def get_total_question_count(self, char_count: int) -> int:
        """
        获取总题目数量
        
        Args:
            char_count: 字符数
            
        Returns:
            总题目数量
        """
        question_counts = self.calculate_question_counts(char_count)
        return sum(question_counts.values())
    
    def format_question_summary(self, question_counts: Dict[str, int]) -> str:
        """
        格式化题目数量摘要
        
        Args:
            question_counts: 题目数量字典
            
        Returns:
            格式化的摘要字符串
        """
        total = sum(question_counts.values())
        summary = f"总计 {total} 道题目: "
        
        parts = []
        if question_counts.get('single_choice_count', 0) > 0:
            parts.append(f"单选 {question_counts['single_choice_count']}")
        if question_counts.get('multiple_choice_count', 0) > 0:
            parts.append(f"多选 {question_counts['multiple_choice_count']}")
        if question_counts.get('fill_blank_count', 0) > 0:
            parts.append(f"填空 {question_counts['fill_blank_count']}")
        if question_counts.get('short_answer_count', 0) > 0:
            parts.append(f"简答 {question_counts['short_answer_count']}")
        if question_counts.get('true_false_count', 0) > 0:
            parts.append(f"判断 {question_counts['true_false_count']}")
        if question_counts.get('sorting_count', 0) > 0:
            parts.append(f"排序 {question_counts['sorting_count']}")
        
        summary += ", ".join(parts)
        return summary
